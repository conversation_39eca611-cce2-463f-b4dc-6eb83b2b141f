<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Detail Jadwal Kuliah</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(to right, #f8f9fa, #dee2e6);
            font-family: 'Segoe UI', sans-serif;
        }

        .card {
            border-radius: 1rem;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
        }

        .card-header {
            background: linear-gradient(to right, #0d6efd, #6610f2);
            color: white;
            border-radius: 1rem 1rem 0 0;
            padding: 25px;
            text-align: center;
        }

        .card-header i {
            font-size: 1.5rem;
            margin-right: 10px;
        }

        .detail-label {
            font-weight: 600;
            color: #343a40;
        }

        .detail-value {
            background-color: #f8f9fa;
            padding: 10px 15px;
            border-radius: 8px;
            margin-bottom: 15px;
            border: 1px solid #dee2e6;
        }

        .btn i {
            margin-right: 5px;
        }
    </style>
</head>

<body>

    <div class="container mt-5 mb-5">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card border-0">
                    <div class="card-header">
                        <h3><i class="bi bi-info-circle-fill"></i> Detail Jadwal Kuliah</h3>
                    </div>
                    <div class="card-body p-4">
                        @php
                            $details = [
                                ['Kode Mata Kuliah', $data->kode_mk],
                                ['Nama Mata Kuliah', $data->nama_mk],
                                ['Jurusan', $data->jurusan],
                                ['Tahun Akademik', $data->tahun_akademik],
                                ['Semester', $data->semester],
                                ['Nama Dosen', $data->nama_dosen],
                                ['Ruang', $data->ruang],
                                ['Hari', $data->hari],
                                ['Jam Mulai', $data->jam_mulai],
                                ['Jam Selesai', $data->jam_selesai],
                            ];
                        @endphp

                        @foreach ($details as [$label, $value])
                            <div>
                                <div class="detail-label">{{ $label }}</div>
                                <div class="detail-value">{{ $value }}</div>
                            </div>
                        @endforeach

                        <div class="mt-4 text-end">
                            <a href="{{ route('crud.index') }}" class="btn btn-secondary btn-lg">
                                <i class="bi bi-arrow-left-circle"></i> Kembali
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>

</html>
