<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data <PERSON><PERSON><PERSON></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(to right, #f8f9fa, #e9ecef);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .card {
            border: none;
            border-radius: 1rem;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.08);
        }

        .table th {
            background-color: #007bff;
            color: white;
            text-align: center;
        }

        .table td {
            vertical-align: middle;
        }

        .btn-custom {
            border-radius: 0.5rem;
            font-weight: 500;
        }

        .btn-sm i {
            margin-right: 5px;
        }

        .header-title {
            background: linear-gradient(to right, #0d6efd, #6610f2);
            color: white;
            padding: 20px;
            border-radius: 1rem 1rem 0 0;
            text-align: center;
        }

        .empty-message {
            background-color: #f8d7da;
            color: #721c24;
            font-weight: 500;
            border-radius: 0.5rem;
        }

        .action-buttons .btn {
            margin-bottom: 5px;
        }

        .pagination {
            justify-content: center;
        }
    </style>
</head>

<body>

    <div class="container mt-5 mb-5">
        <div class="card">
            <div class="header-title">
                <h2 class="mb-0 fw-bold">📘 Jadwal Kuliah Mahasiswa</h2>
            </div>
            <div class="card-body p-4">

                <div class="d-flex justify-content-end mb-3">
                    <a href="{{ route('crud.create') }}" class="btn btn-success btn-custom">
                        <i class="bi bi-plus-circle"></i> Tambah Jadwal
                    </a>
                </div>

                <div class="table-responsive">
                    <table class="table table-bordered table-hover align-middle">
                        <thead>
                            <tr>
                                <th>Kode MK</th>
                                <th>Nama MK</th>
                                <th>Jurusan</th>
                                <th>Tahun Akademik</th>
                                <th>Semester</th>
                                <th>Nama Dosen</th>
                                <th>Ruang</th>
                                <th>Hari</th>
                                <th>Jam Mulai</th>
                                <th>Jam Selesai</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse ($dataArray as $data)
                                <tr>
                                    <td>{{ $data->kode_mk }}</td>
                                    <td>{{ $data->nama_mk }}</td>
                                    <td>{{ $data->jurusan }}</td>
                                    <td>{{ $data->tahun_akademik }}</td>
                                    <td>{{ $data->semester }}</td>
                                    <td>{{ $data->nama_dosen }}</td>
                                    <td>{{ $data->ruang }}</td>
                                    <td>{{ $data->hari }}</td>
                                    <td>{{ $data->jam_mulai }}</td>
                                    <td>{{ $data->jam_selesai }}</td>
                                    <td class="text-center action-buttons">
                                        <form onsubmit="return confirm('Apakah Anda yakin?');" action="{{ route('crud.destroy', $data->id) }}" method="POST">
                                            <a href="{{ route('crud.show', $data->id) }}" class="btn btn-sm btn-outline-dark btn-custom">
                                                <i class="bi bi-eye-fill"></i> Lihat
                                            </a>
                                            <a href="{{ route('crud.edit', $data->id) }}" class="btn btn-sm btn-outline-primary btn-custom">
                                                <i class="bi bi-pencil-square"></i> Edit
                                            </a>
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-outline-danger btn-custom">
                                                <i class="bi bi-trash-fill"></i> Hapus
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="11" class="text-center">
                                        <div class="empty-message p-3">
                                            <i class="bi bi-exclamation-circle-fill"></i> Data belum tersedia.
                                        </div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <div class="mt-4">
                    {{ $dataArray->links() }}
                </div>

            </div>
        </div>
    </div>

    <!-- Bootstrap & SweetAlert JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
        @if(session()->has('success'))
            Swal.fire({
                icon: "success",
                title: "Berhasil",
                text: "{{ session('success') }}",
                showConfirmButton: false,
                timer: 2000
            });
        @elseif(session()->has('error'))
            Swal.fire({
                icon: "error",
                title: "Gagal",
                text: "{{ session('error') }}",
                showConfirmButton: false,
                timer: 2000
            });
        @endif
    </script>

</body>

</html>
