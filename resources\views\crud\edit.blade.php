<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit <PERSON><PERSON><PERSON></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(to right, #f8f9fa, #e9ecef);
            font-family: 'Segoe UI', sans-serif;
        }

        .card {
            border-radius: 1rem;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        }

        .card-header {
            background: linear-gradient(to right, #0d6efd, #6610f2);
            color: white;
            border-radius: 1rem 1rem 0 0;
            padding: 20px;
            text-align: center;
        }

        label {
            font-weight: 600;
        }

        .btn i {
            margin-right: 5px;
        }
    </style>
</head>

<body>

    <div class="container mt-5 mb-5">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card border-0 shadow-sm">
                    <div class="card-header">
                        <h3 class="mb-0">✏️ Edit Jadwal Kuliah</h3>
                    </div>
                    <div class="card-body p-4">
                        <form action="{{ route('crud.update', $data->id) }}" method="POST">
                            @csrf
                            @method('PUT')

                            <div class="row">
                                @php
                                    $fields = [
                                        ['kode_mk', 'Kode Mata Kuliah', 'text', $data->kode_mk],
                                        ['nama_mk', 'Nama Mata Kuliah', 'text', $data->nama_mk],
                                        ['jurusan', 'Jurusan', 'text', $data->jurusan],
                                        ['tahun_akademik', 'Tahun Akademik', 'text', $data->tahun_akademik],
                                        ['semester', 'Semester', 'text', $data->semester],
                                        ['nama_dosen', 'Nama Dosen', 'text', $data->nama_dosen],
                                        ['ruang', 'Ruang', 'text', $data->ruang],
                                        ['hari', 'Hari', 'text', $data->hari],
                                        ['jam_mulai', 'Jam Mulai', 'time', $data->jam_mulai],
                                        ['jam_selesai', 'Jam Selesai', 'time', $data->jam_selesai],
                                    ];
                                @endphp

                                @foreach ($fields as [$name, $label, $type, $value])
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label>{{ $label }}</label>
                                            <input type="{{ $type }}"
                                                class="form-control @error($name) is-invalid @enderror"
                                                name="{{ $name }}"
                                                value="{{ old($name, $value) }}"
                                                placeholder="Masukkan {{ $label }}">
                                            @error($name)
                                                <div class="alert alert-danger mt-2">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                @endforeach
                            </div>

                            <div class="mt-4">
                                <button type="submit" class="btn btn-primary me-2 btn-lg">
                                    <i class="bi bi-save"></i> Update
                                </button>
                                <button type="reset" class="btn btn-warning me-2 btn-lg">
                                    <i class="bi bi-arrow-clockwise"></i> Reset
                                </button>
                                <a href="{{ route('crud.index') }}" class="btn btn-secondary btn-lg">
                                    <i class="bi bi-arrow-left"></i> Kembali
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>

</html>
