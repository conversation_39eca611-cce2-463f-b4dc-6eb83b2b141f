<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>bah Jadwal Kuliah</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(to right, #f8f9fa, #e9ecef);
            font-family: 'Segoe UI', sans-serif;
        }

        .card {
            border-radius: 1rem;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        }

        .card-header {
            background: linear-gradient(to right, #0d6efd, #6610f2);
            color: white;
            border-radius: 1rem 1rem 0 0;
            padding: 20px;
            text-align: center;
        }

        label {
            font-weight: 600;
        }

        .btn i {
            margin-right: 5px;
        }
    </style>
</head>

<body>

    <div class="container mt-5 mb-5">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card border-0 shadow-sm">
                    <div class="card-header">
                        <h3 class="mb-0">📝 Tambah Jadwal Kuliah</h3>
                    </div>
                    <div class="card-body p-4">
                        <form action="{{ route('crud.store') }}" method="POST">
                            @csrf

                            <div class="row">
                                @php
                                    $fields = [
                                        ['kode_mk', 'Kode Mata Kuliah', 'text'],
                                        ['nama_mk', 'Nama Mata Kuliah', 'text'],
                                        ['jurusan', 'Jurusan', 'text'],
                                        ['tahun_akademik', 'Tahun Akademik', 'text'],
                                        ['semester', 'Semester', 'text'],
                                        ['nama_dosen', 'Nama Dosen', 'text'],
                                        ['ruang', 'Ruang', 'text'],
                                        ['hari', 'Hari', 'text'],
                                        ['jam_mulai', 'Jam Mulai', 'time'],
                                        ['jam_selesai', 'Jam Selesai', 'time'],
                                    ];
                                @endphp

                                @foreach ($fields as [$name, $label, $type])
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label>{{ $label }}</label>
                                            <input type="{{ $type }}"
                                                class="form-control @error($name) is-invalid @enderror"
                                                name="{{ $name }}"
                                                value="{{ old($name) }}"
                                                placeholder="Masukkan {{ $label }}">
                                            @error($name)
                                                <div class="alert alert-danger mt-2">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                @endforeach
                            </div>

                            <div class="mt-4">
                                <button type="submit" class="btn btn-success me-2 btn-lg">
                                    <i class="bi bi-plus-circle"></i> Simpan
                                </button>
                                <button type="reset" class="btn btn-warning me-2 btn-lg">
                                    <i class="bi bi-arrow-clockwise"></i> Reset
                                </button>
                                <a href="{{ route('crud.index') }}" class="btn btn-secondary btn-lg">
                                    <i class="bi bi-arrow-left"></i> Kembali
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>

</html>
