<?php

namespace App\Http\Controllers;

use App\Models\JadwalKuliahDavy;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;

class JadwalKuliahDavyController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $dataArray = JadwalKuliahDavy::latest()->paginate(10);
        return view('crud/index', compact('dataArray'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('crud/create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'kode_mk' => 'required',
            'nama_mk' => 'required',
            'jurusan' => 'required',
            'tahun_akademik' => 'required',
            'semester' => 'required',
            'nama_dosen' => 'required',
            'ruang' => 'required',
            'hari' => 'required',
            'jam_mulai' => 'required',
            'jam_selesai' => 'required',
        ]);

        JadwalKuliahDavy::create($request->all());

        return redirect()->route('crud.index')->with('success', 'Jadwal Kuliah Berhasil Ditambahkan');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id): View
    {
        $data = JadwalKuliahDavy::findOrFail($id);
        return view('crud.show', compact('data'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id): View
    {
        $data = JadwalKuliahDavy::findOrFail($id);
        return view('crud.edit', compact('data'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id): RedirectResponse
    {
        $request->validate([
            'kode_mk' => 'required',
            'nama_mk' => 'required',
            'jurusan' => 'required',
            'tahun_akademik' => 'required',
            'semester' => 'required',
            'nama_dosen' => 'required',
            'ruang' => 'required',
            'hari' => 'required',
            'jam_mulai' => 'required',
            'jam_selesai' => 'required',
        ]);

        $data = JadwalKuliahDavy::findOrFail($id);
        $data->update($request->all());

        return redirect()->route('crud.index')->with('success', 'Jadwal Kuliah Berhasil Diupdate');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id): RedirectResponse
    {
        $data = JadwalKuliahDavy::findOrFail($id);
        $data->delete();

        return redirect()->route('crud.index')->with('success', 'Jadwal Kuliah Berhasil Dihapus');
    }
}
